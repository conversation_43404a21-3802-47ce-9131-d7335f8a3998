import { PetEntity } from "../../entities/pets.entity";
import { OwnerAttributes, OwnerEntity, OwnerInfo } from "../../entities/owners.entity";
import {
    ModuleError,
    FormatValidationError,
    DefaultError,
    EntityError,
    ServiceError,
    RepositoryError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e, DatabaseErrorEnum as d } from "../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../enum/response";
import { PetRequest, PetInfoRequest, CreatePetWithOwnerRequest, GetPetListFilterRequest } from "./dto/request";
import {
    PetWithOwnerListResponse,
    PetResponse,
    PetWithOwnerByIdResponse,
    CreatePetWithOwnerResponse,
    PetResolveError,
    PetNotFoundError,
} from "./dto/response";
import { PetRepository } from "../../repositories/pets.repository";
import { OwnerRepository } from "../../repositories/owners.repository";
import { OwnerResolveError } from "../owners/dto/response";
import { ResponseValidationMessageEnum as v, ResponseValidationDuplicate as r } from "../../enum/validate";

export class PetModule {
    private readonly petRepository;
    private readonly ownerRepository;
    constructor() {
        this.petRepository = new PetRepository();
        this.ownerRepository = new OwnerRepository();
    }

    public async getOne(id: string): Promise<PetWithOwnerByIdResponse> {
        try {
            const errorRes: PetNotFoundError = { id: d.NOT_FOUND };

            const validateId = new PetEntity(id);
            const validId = validateId.getId();

            const response = await this.petRepository.findPetById(validId);

            if (!response) {
                throw new DefaultError(e.DATABASE_ERROR, m.ID_NOT_FOUND, null, errorRes, s.NOT_FOUND);
            }

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_PET_FAILED);
        }
    }

    public async getAll(): Promise<PetResponse[]> {
        try {
            const response = await this.petRepository.findAll();
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_PET_FAILED);
        }
    }

    public async getList(
        limit: number,
        page: number,
        filter?: GetPetListFilterRequest
    ): Promise<PetWithOwnerListResponse> {
        try {
            if (filter) {
                const petEntity = new PetEntity(null, null);
                petEntity.validateFilter(filter);
            }

            const response = await this.petRepository.findList(limit, page, filter);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_PET_FAILED);
        }
    }

    public async calculateBCSScore(scores: number[]): Promise<number> {
        try {
            const petEntity = new PetEntity();
            const bcsScore = petEntity.calculateBCSScore(scores);
            return bcsScore;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CALCULATE_BCS_SCORE_FAILED);
        }
    }

    public async createWithOwner(data: CreatePetWithOwnerRequest): Promise<CreatePetWithOwnerResponse> {
        try {
            const ownerData = data.ownerDetails?.ownerInfo ?? null;

            const ownerId = await this.createValidateAndResolveOwnerId(ownerData, data.petDetails.ownerId);

            const createdPet = await this.createValidatedPet(data.petDetails.petInfo, ownerId);

            const ownerDetails = await this.ownerRepository.findOwnerById(ownerId);

            if (!ownerDetails) {
                throw new ModuleError(m.OWNER_ID_NOT_FOUND);
            }

            const response = {
                pet: createdPet,
                owner: ownerDetails,
            };

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors, s.BAD_REQUEST);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ModuleError) {
                throw new DefaultError(e.MODULE_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_PET_WITH_OWNER_FAILED);
        }
    }

    public async update(id: string, data: PetRequest): Promise<boolean> {
        try {
            const petData = data;

            const petEntity = new PetEntity(id, petData);
            const validId = petEntity.getId();

            const petAttributes = petEntity.getAttributes();

            const response = await this.petRepository.update(validId, petAttributes);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_PET_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const validateId = new PetEntity(id);
            const validId = validateId.getId();

            const data = {
                isDeleted: true,
                deletedAt: new Date(),
            };

            const response = await this.petRepository.update(validId, data);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_PET_FAILED);
        }
    }

    // Private helper method
    private async createValidateAndResolveOwnerId(
        ownerInfo: OwnerInfo | null,
        existingOwnerId: string | null
    ): Promise<string> {
        const errorRes: OwnerResolveError = {
            ownerInfo: {},
        };

        if (existingOwnerId && !ownerInfo) return existingOwnerId;

        if (ownerInfo && !existingOwnerId) {
            const ownerData = {
                ownerInfo: ownerInfo,
            } as OwnerAttributes;
            const ownerEntity = new OwnerEntity(null, ownerData);

            const validOwner = ownerEntity.getAttributes();

            const existingOwner = await this.ownerRepository.findByPhoneNumber(validOwner.ownerInfo.phoneNumber);

            if (existingOwner) errorRes.ownerInfo = { phoneNumber: r.DUPLICATED_PHONE };

            if (Object.keys(errorRes.ownerInfo).length > 0) {
                throw new DefaultError(e.DATABASE_ERROR, m.DUPLICATED_DATA, null, errorRes, s.CONFLICT);
            }
            const createdOwner = await this.ownerRepository.create(validOwner);
            return createdOwner.id;
        }

        if (existingOwnerId && ownerInfo) {
            errorRes.ownerInfo = v.OWNER_INFO;
            throw new DefaultError(e.VALIDATION_ERROR, m.OWNER_INFO_AND_ID_CONFLICT, null, errorRes, s.BAD_REQUEST);
        }

        if (!existingOwnerId && !ownerInfo) {
            errorRes.ownerInfo = v.OWNER_INFO;
            throw new DefaultError(e.VALIDATION_ERROR, m.OWNER_INFO_OR_ID_REQUIRED, null, errorRes, s.BAD_REQUEST);
        }
        throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.OWNER_RESOLVE_FAILED);
    }

    private async createValidatedPet(petInfo: PetInfoRequest, ownerId: string): Promise<PetResponse> {
        const errorRes: PetResolveError = {
            petInfo: {},
        };

        const petData = {
            ownerId: ownerId,
            petInfo: petInfo,
        };
        const petEntity = new PetEntity(null, petData);

        const validPet = petEntity.getAttributes();

        const existingPet = await this.petRepository.findByHN(validPet.petInfo.hn);

        if (existingPet) errorRes.petInfo = { hn: r.DUPLICATED_HNID };

        if (Object.keys(errorRes.petInfo).length > 0) {
            throw new DefaultError(e.DATABASE_ERROR, m.DUPLICATED_DATA, null, errorRes, s.CONFLICT);
        }

        const createdPet = await this.petRepository.create(validPet);
        return createdPet;
    }
}
