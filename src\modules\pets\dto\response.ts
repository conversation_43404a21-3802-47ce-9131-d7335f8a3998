import { OwnerResponse } from "../../owners/dto/response";
import { PetInfo } from "../../../entities/pets.entity";

export type PetResponse = {
    id?: string;
    ownerId: string;
    petInfo: PetInfo;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type CreatePetWithOwnerResponse = {
    pet: PetResponse;
    owner: OwnerResponse;
};

export type PetWithOwnerResponse = {
    petInfo: {
        hn: string;
        name: string;
        gender: string;
        species: string;
        breed: string;
    };
    id: string;
    ownerId: string;
    ownerInfo: {
        firstName: string;
        lastName: string;
        phoneNumber: string;
    };
};

export type PetWithOwnerByIdResponse = {
    id: string;
    ownerId: string;
    petInfo: PetInfo;
    ownerInfo: {
        firstName: string;
        lastName: string;
        phoneNumber: string;
        email: string;
    };
    isDeleted: boolean;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
};

export type PetWithOwnerListResponse = {
    rows: PetWithOwnerResponse[];
    count: number;
};

export type PetResolveError = {
    petInfo: { hn?: string };
};

export type PetNotFoundError = {
    id: string;
};
